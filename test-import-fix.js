// Simple test to verify the import fixes work
const fs = require('fs');

// Test CSV content with BOM character and header row
const testCsvContent = `﻿#,Code,Customer,Sharing,Purchase Date,Reference No.,Item Code,Item,Item Remark,Consumed,Balance,Qty,Value (SGD),Last Consumed,Expiry Date,Price (SGD),Total (SGD),Purchased At,Prepaid Remark
1,66186,DONALD PIRET,,04-05-2021,TK100706: IN100647,S01094,MAY 2021 MEMBER : AROMA 90MINS @ $124.20 ( U.P $138 ),,0 Mins,180 Mins,180 Mins,248.40,,,124.20,248.40,[HQ],
2,66186,<PERSON><PERSON><PERSON><PERSON> PIRET,,20-12-2022,TK269722: IN269611,S01008,Onsen 1 for 1 (M),,0,1,1,0.00,,,0.00,0.00,[HQ],`;

// Test the header detection logic
function testHeaderDetection() {
  console.log('Testing header detection logic...');
  
  const lines = testCsvContent.split('\n');
  const headerLine = lines[0];
  
  // Simulate the CSV parsing
  const columns = headerLine.split(',');
  const data = {
    index: columns[0],
    code: columns[1],
    customer: columns[2]
  };
  
  console.log('Header data:', data);
  
  // Test our improved header detection logic
  const cleanIndex = data.index?.replace(/^\uFEFF/, '').trim(); // Remove BOM
  const cleanCode = data.code?.trim();
  const cleanCustomer = data.customer?.trim();
  
  const shouldSkip = (
    cleanIndex === '#' ||
    cleanIndex === 'index' ||
    cleanCode === 'Code' ||
    cleanCustomer === 'Customer' ||
    cleanCustomer === 'Grand Total' ||
    cleanCustomer?.toLowerCase().includes('customer') ||
    !cleanCode ||
    cleanCode === ''
  );
  
  console.log('Clean index:', cleanIndex);
  console.log('Clean code:', cleanCode);
  console.log('Clean customer:', cleanCustomer);
  console.log('Should skip header:', shouldSkip);
  
  if (shouldSkip) {
    console.log('✅ Header detection works correctly - header row will be skipped');
  } else {
    console.log('❌ Header detection failed - header row would be processed as data');
  }
}

// Test the data row processing
function testDataRowProcessing() {
  console.log('\nTesting data row processing...');
  
  const lines = testCsvContent.split('\n');
  const dataLine1 = lines[1];
  const dataLine2 = lines[2];
  
  // Test first data row (non-zero values)
  const columns1 = dataLine1.split(',');
  const data1 = {
    index: columns1[0],
    code: columns1[1],
    customer: columns1[2],
    price: columns1[15],
    total: columns1[16]
  };
  
  console.log('Data row 1:', data1);
  
  const cleanIndex1 = data1.index?.replace(/^\uFEFF/, '').trim();
  const cleanCode1 = data1.code?.trim();
  const shouldSkip1 = (
    cleanIndex1 === '#' ||
    cleanCode1 === 'Code' ||
    !cleanCode1 ||
    cleanCode1 === ''
  );
  
  console.log('Should skip data row 1:', shouldSkip1);
  
  // Test second data row (zero values)
  const columns2 = dataLine2.split(',');
  const data2 = {
    index: columns2[0],
    code: columns2[1],
    customer: columns2[2],
    price: parseFloat(columns2[15]),
    total: parseFloat(columns2[16])
  };
  
  console.log('Data row 2:', data2);
  
  // Test our improved validation (should allow zero values)
  const priceValid = data2.price >= 0; // Changed from > 0 to >= 0
  const totalValid = data2.total >= 0; // Changed from > 0 to >= 0
  
  console.log('Price valid (>= 0):', priceValid);
  console.log('Total valid (>= 0):', totalValid);
  
  if (priceValid && totalValid) {
    console.log('✅ Zero value validation works correctly - promotional items with $0 are allowed');
  } else {
    console.log('❌ Zero value validation failed');
  }
}

// Run tests
console.log('=== Testing Import Fixes ===\n');
testHeaderDetection();
testDataRowProcessing();
console.log('\n=== Test Complete ===');
